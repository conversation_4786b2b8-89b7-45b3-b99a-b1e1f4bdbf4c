import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { getUserGenerations } from "@/models/music-generation";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.uuid) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = parseInt(searchParams.get("offset") || "0");
    const status = searchParams.get("status");

    const generations = await getUserGenerations(
      session.user.uuid,
      limit,
      offset,
      status as any
    );
    
    return NextResponse.json({
      generations,
      success: true
    });
  } catch (error) {
    console.error("Failed to get user generations:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
