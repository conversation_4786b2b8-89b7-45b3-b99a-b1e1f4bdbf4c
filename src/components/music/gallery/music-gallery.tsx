"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Filter, 
  Music, 
  Plus,
  Download,
  Trash2,
  Edit,
  Share2,
  Clock,
  Calendar,
  Folder,
  Grid3X3,
  List,
  SortAsc,
  SortDesc
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import TrackCard from "@/components/music/track/track-card";
import { Track } from "@/types/music";
import Link from "next/link";

interface UserTrack extends Track {
  generation_status?: "pending" | "processing" | "completed" | "failed";
  is_favorite?: boolean;
  folder_id?: string;
  tags?: string[];
}

export default function MusicGallery() {
  const { data: session, status } = useSession();
  const [tracks, setTracks] = useState<UserTrack[]>([]);
  const [filteredTracks, setFilteredTracks] = useState<UserTrack[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedStyle, setSelectedStyle] = useState("all");
  const [sortBy, setSortBy] = useState("recent");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTracks, setSelectedTracks] = useState<Set<string>>(new Set());

  // Load user's tracks
  useEffect(() => {
    if (status === "authenticated" && session?.user) {
      loadUserTracks();
    }
  }, [status, session]);

  const loadUserTracks = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/music/generations");
      const data = await response.json();

      if (data.code === 0) {
        setTracks(data.data.generations || []);
      } else {
        toast.error("Failed to load your music library");
      }
    } catch (error) {
      console.error("Failed to load tracks:", error);
      toast.error("Failed to load your music library");
    } finally {
      setIsLoading(false);
    }
  };

  // Filter and search logic
  useEffect(() => {
    let filtered = [...tracks];

    // Search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(track => 
        track.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.prompt?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.style?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.mood?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Status filter
    if (selectedStatus !== "all") {
      filtered = filtered.filter(track => track.generation_status === selectedStatus);
    }

    // Style filter
    if (selectedStyle !== "all") {
      filtered = filtered.filter(track => track.style === selectedStyle);
    }

    // Sort
    switch (sortBy) {
      case "recent":
        filtered.sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime());
        break;
      case "oldest":
        filtered.sort((a, b) => new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime());
        break;
      case "title":
        filtered.sort((a, b) => (a.title || "").localeCompare(b.title || ""));
        break;
      case "duration":
        filtered.sort((a, b) => (b.duration || 0) - (a.duration || 0));
        break;
    }

    setFilteredTracks(filtered);
  }, [tracks, searchQuery, selectedStatus, selectedStyle, sortBy]);

  const handleTrackPlay = (track: UserTrack) => {
    console.log("Playing track:", track.title);
    // TODO: Implement global audio player
  };

  const handleTrackDownload = async (track: UserTrack) => {
    if (!track.file_url) {
      toast.error("Track file not available");
      return;
    }

    try {
      const link = document.createElement("a");
      link.href = track.file_url;
      link.download = `${track.title || "track"}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success("Download started");
    } catch (error) {
      toast.error("Download failed");
    }
  };

  const handleTrackDelete = async (track: UserTrack) => {
    if (!confirm("Are you sure you want to delete this track?")) {
      return;
    }

    try {
      // TODO: Implement delete API
      setTracks(prev => prev.filter(t => t.uuid !== track.uuid));
      toast.success("Track deleted successfully");
    } catch (error) {
      toast.error("Failed to delete track");
    }
  };

  const handleTrackShare = (track: UserTrack) => {
    const shareUrl = `${window.location.origin}/loops/${track.uuid}`;
    navigator.clipboard.writeText(shareUrl);
    toast.success("Share link copied to clipboard");
  };

  const handleBulkAction = (action: "download" | "delete" | "share") => {
    const selected = tracks.filter(track => selectedTracks.has(track.uuid));
    
    switch (action) {
      case "download":
        selected.forEach(track => handleTrackDownload(track));
        break;
      case "delete":
        if (confirm(`Delete ${selected.length} selected tracks?`)) {
          selected.forEach(track => handleTrackDelete(track));
        }
        break;
      case "share":
        // TODO: Implement bulk share
        toast.info("Bulk share feature coming soon");
        break;
    }
    
    setSelectedTracks(new Set());
  };

  const toggleTrackSelection = (trackId: string) => {
    const newSelection = new Set(selectedTracks);
    if (newSelection.has(trackId)) {
      newSelection.delete(trackId);
    } else {
      newSelection.add(trackId);
    }
    setSelectedTracks(newSelection);
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedStatus("all");
    setSelectedStyle("all");
    setSortBy("recent");
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case "completed":
        return <Badge variant="default" className="text-xs">Completed</Badge>;
      case "processing":
        return <Badge variant="secondary" className="text-xs">Processing</Badge>;
      case "pending":
        return <Badge variant="outline" className="text-xs">Pending</Badge>;
      case "failed":
        return <Badge variant="destructive" className="text-xs">Failed</Badge>;
      default:
        return null;
    }
  };

  if (status === "loading") {
    return (
      <div className="container py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your music library...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">My Music Library</h1>
          <p className="text-muted-foreground">
            Manage your AI-generated music loops and collections
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Link href="/generate">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create New Track
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Music className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Total Tracks</p>
                <p className="text-2xl font-bold">{tracks.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Total Duration</p>
                <p className="text-2xl font-bold">
                  {Math.round(tracks.reduce((sum, track) => sum + (track.duration || 0), 0) / 60)}m
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Download className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">
                  {tracks.filter(t => t.generation_status === "completed").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold">
                  {tracks.filter(t => {
                    const created = new Date(t.created_at || 0);
                    const now = new Date();
                    return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter & Search
            </CardTitle>
            
            <div className="flex items-center gap-2">
              {/* View Mode Toggle */}
              <div className="flex items-center border rounded-md">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search your tracks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>

            {/* Style Filter */}
            <Select value={selectedStyle} onValueChange={setSelectedStyle}>
              <SelectTrigger>
                <SelectValue placeholder="Style" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Styles</SelectItem>
                <SelectItem value="electronic">Electronic</SelectItem>
                <SelectItem value="ambient">Ambient</SelectItem>
                <SelectItem value="corporate">Corporate</SelectItem>
                <SelectItem value="cinematic">Cinematic</SelectItem>
                <SelectItem value="acoustic">Acoustic</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="title">Title A-Z</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
          </div>

          {/* Bulk Actions */}
          {selectedTracks.size > 0 && (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
              <span className="text-sm text-muted-foreground">
                {selectedTracks.size} track(s) selected
              </span>
              <div className="flex items-center gap-2 ml-auto">
                <Button size="sm" variant="outline" onClick={() => handleBulkAction("download")}>
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleBulkAction("share")}>
                  <Share2 className="h-4 w-4 mr-1" />
                  Share
                </Button>
                <Button size="sm" variant="destructive" onClick={() => handleBulkAction("delete")}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {filteredTracks.length} of {tracks.length} tracks
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {isLoading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your tracks...</p>
        </div>
      ) : tracks.length === 0 ? (
        <div className="text-center py-12">
          <Music className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tracks yet</h3>
          <p className="text-muted-foreground mb-4">
            Start creating your first AI music loop
          </p>
          <Link href="/generate">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Track
            </Button>
          </Link>
        </div>
      ) : filteredTracks.length === 0 ? (
        <div className="text-center py-12">
          <Music className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tracks found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your filters or search terms
          </p>
          <Button variant="outline" onClick={clearFilters}>
            Clear Filters
          </Button>
        </div>
      ) : (
        <div className={cn(
          viewMode === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
        )}>
          {filteredTracks.map((track) => (
            <div key={track.uuid} className="relative">
              {/* Selection checkbox for grid view */}
              {viewMode === "grid" && (
                <div className="absolute top-2 left-2 z-10">
                  <input
                    type="checkbox"
                    checked={selectedTracks.has(track.uuid)}
                    onChange={() => toggleTrackSelection(track.uuid)}
                    className="rounded border-gray-300"
                  />
                </div>
              )}
              
              <div className="relative">
                <TrackCard
                  track={track}
                  showUser={false}
                  onPlay={handleTrackPlay}
                  onDownload={handleTrackDownload}
                  onShare={handleTrackShare}
                />

                {/* Custom actions overlay */}
                <div className="absolute top-2 right-2 flex items-center gap-1">
                  {getStatusBadge(track.generation_status)}
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleTrackDelete(track)}
                    className="text-destructive hover:text-destructive bg-background/80 backdrop-blur-sm"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
